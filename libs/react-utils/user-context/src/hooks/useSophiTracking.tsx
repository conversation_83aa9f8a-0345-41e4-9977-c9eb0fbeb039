import { MetaProps } from '@benzinga/seo';
import { WordpressPage, WordpressPost } from '@benzinga/content-manager';
import { useUser } from './useUser';
import { useCallback, useContext, useState, useMemo } from 'react';
import { sophiManager } from '@benzinga/ads-utils';
import { SessionContext } from '@benzinga/session-context';
import { useSophiPageView } from './useSophiPageView';
import { usePathname } from 'next/navigation';

export interface SophiDecision {
  outcome?: {
    wallVisibility?: 'always' | 'never' | string;
    wallType?: string;
  };
}

export interface SophiPageTrackingOptions {
  meta?: MetaProps;
  post?: WordpressPage | WordpressPost;
  article?: any;
  disabled?: boolean;
}

export const useSophiTracking = ({ article, disabled = false, meta, post }: SophiPageTrackingOptions) => {
  const session = useContext(SessionContext);
  const user = useUser();
  const pathname = usePathname();
  const [sophiDecision, setSophiDecision] = useState<SophiDecision | null>(null);

  const pageType = useMemo(() => {
    if (article) return 'article';
    if (post) {
      if (post.slug === 'benzinga-home-page') return 'home-page';
      return post.post_type || 'page';
    }
    return 'page';
  }, [article, post]);

  const section = useMemo(() => {
    if (article?.channels?.[0]?.name) return article.channels[0].name;
    if (meta?.structuredData?.articleSection) return meta.structuredData.articleSection;
    if (pathname === '/') return 'homepage';
    return '';
  }, [article?.channels, meta?.structuredData?.articleSection, pathname]);

  const trackingData = useMemo(() => {
    const baseData = {
      page_referrer: typeof document !== 'undefined' ? document.referrer : '',
      page_title: meta?.title || '',
      page_type: pageType,
      page_url: meta?.canonical,
    };

    if (article) {
      return {
        ...baseData,
        article_author: article.author?.name || '',
        article_id: String(article.nodeId || ''),
        article_published_date: article.createdAt,
        article_title: article.title || '',
      };
    }

    if (post) {
      return {
        ...baseData,
        author: post.author?.name || '',
        id: String(post.id || ''),
        title: post.title || '',
      };
    }

    return baseData;
  }, [article, post, meta?.title, meta?.canonical, pageType]);

  useSophiPageView({
    disabled,
    isArticlePage: pageType === 'article',
    pageType,
    section,
    trackingData,
  });

  const getDecision = useCallback(async (): Promise<SophiDecision> => {
    if (disabled || !user) {
      return { outcome: { wallType: 'paywall', wallVisibility: 'never' } };
    }

    const userType = user.accessType === 'anonymous' ? 'anonymous' : 'registered';
    try {
      const decision = await sophiManager.getDecision(userType);
      console.log('[Sophi] Decision received:', decision);
      setSophiDecision(decision);
      return decision;
    } catch (error) {
      console.error('[Sophi] Error getting decision:', error);
      const fallbackDecision = { outcome: { wallType: 'paywall', wallVisibility: 'never' } };
      setSophiDecision(fallbackDecision);
      return fallbackDecision;
    }
  }, [user, disabled]);

  const trackWallHit = useCallback(
    (wallType: 'paywall' | 'regwall', additionalData: Record<string, any> = {}) => {
      if (disabled) return;

      console.log('[Sophi] Tracking wall encounter, type:', wallType);
      sophiManager.trackWallHit(wallType, section);

      const wallHitData = {
        page_section: section,
        page_type: pageType,
        wall_type: wallType,
        ...additionalData,
      };

      console.log('[Sophi] Tracking wall hit with data:', wallHitData);
      sophiManager.trackWithData(session, 'wallhit', wallHitData);
    },
    [disabled, section, pageType, session],
  );

  return {
    getDecision,
    pageType,
    section,
    sophiDecision,
    trackWallHit,
    trackingData,
  };
};
